import 'package:crud/controllers/counter_controller.dart';
import 'package:flutter/material.dart';
import 'package:refreshed/refreshed.dart';

class HomeView extends StatelessWidget {
  HomeView({super.key});

  final controller = Get.put(CounterController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Center(child: Obx(() => Text('Hello World! - ${controller.count}'))),
          FloatingActionButton.small(
            onPressed: () => controller.increment(),
            child: const Icon(Icons.add),
          ),
        ],
      ),
    );
  }
}
